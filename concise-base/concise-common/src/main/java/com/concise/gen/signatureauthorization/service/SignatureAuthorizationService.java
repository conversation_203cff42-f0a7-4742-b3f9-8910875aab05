package com.concise.gen.signatureauthorization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.signatureauthorization.entity.SignatureAuthorization;
import com.concise.gen.signatureauthorization.param.SignatureAuthorizationParam;

import java.util.List;
import java.util.Set;

/**
 * 签章授权表service接口
 *
 * <AUTHOR>
 * @date 2025-07-31 11:04:05
 */
public interface SignatureAuthorizationService extends IService<SignatureAuthorization> {

    /**
     * 查询签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    PageResult<SignatureAuthorization> page(SignatureAuthorizationParam signatureAuthorizationParam);

    /**
     * 签章授权表列表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    List<SignatureAuthorization> list(SignatureAuthorizationParam signatureAuthorizationParam);

    /**
     * 添加签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    void add(SignatureAuthorizationParam signatureAuthorizationParam);

    /**
     * 删除签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    void delete(SignatureAuthorizationParam signatureAuthorizationParam);

    /**
     * 编辑签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    void edit(SignatureAuthorizationParam signatureAuthorizationParam);

    /**
     * 查看签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    SignatureAuthorization detail(SignatureAuthorizationParam signatureAuthorizationParam);

    /**
     * 根据签章id获取授权记录
     */
    List<SignatureAuthorization> listBySignatureId(String signatureId);

    /**
     * 判断当前用户是否有权限使用该印章
     *
     * @param signatureId 签章id
     * @param userId      用户id
     * @param realName    用户真实姓名
     * @return
     */
    boolean hasAuthorization(String signatureId, String userId, String realName);

    /**
     * 盖章
     *
     * @param sealNo
     * @param pdfId
     * @return
     */
    SysFileInfo seal(String sealNo, String pdfId);

    /**
     * 批量查询已共享的id
     */
    Set<String> getSharedIds();
}
